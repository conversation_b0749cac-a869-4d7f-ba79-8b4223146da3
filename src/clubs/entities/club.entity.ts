import { ObjectType, registerEnumType, Field } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Address, DayOfWeek, MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.input';
import { ClubCategory } from './club-categories.entity';
import mongoose from 'mongoose';
import { Contact } from 'src/users/entities/user.entity';
import { City } from 'src/city/entities/city.entity';

export enum ClubStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
}

registerEnumType(ClubStatus, { name: 'ClubStatus' });

@ObjectType()
export class DayTiming {
  /** Day of the week */
  day: DayOfWeek;

  @Field(() => [Number])
  /** Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30) */
  timings: [number, number];
}

@ObjectType()
export class BusinessHoursSchedule {
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  schedule: DayTiming[];
}

export enum MenuItemAvailability {
  AVAILABLE = 'AVAILABLE',
  UNAVAILABLE = 'UNAVAILABLE',
}
registerEnumType(MenuItemAvailability, { name: 'MenuItemAvailability' });

@ObjectType()
export class MenuItem {
  /** Menu item name */
  name: string;

  /** Menu item price */
  price: number;

  /** Whether the item is available */
  available: MenuItemAvailability;
}

@ObjectType()
export class Menu {
  /** Menu currency (e.g., USD, EUR, INR) */
  currency: string;

  /** Array of menu items */
  items: MenuItem[];
}

@ObjectType()
@Schema()
export class Club extends MongooseSchema {
  /** Club name */
  @Prop({ required: true })
  name: string;

  /** Club description */
  @Prop({ required: true })
  description: string;

  /** Club category */
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  @Field(() => [ClubCategory])
  categories: [ClubCategory];

  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  @Field(() => [City])
  city: City[];

  /** Club status */
  @Prop({
    required: true,
    enum: Object.values(ClubStatus),
    default: ClubStatus.PENDING,
  })
  status: ClubStatus;

  /** Club logo URL */
  @Prop()
  logo?: string;

  /** Club cover image URL */
  @Prop()
  coverImage?: string;

  /** Array of club image URLs */
  @Prop({ required: true, type: [String] })
  images: string[];

  /** Club phone number */
  @Prop({ type: Object })
  phone?: Contact;

  /** Club address */
  @Prop({ type: Object })
  address?: Address;

  /** Opening hours */
  @Prop({ type: Object })
  businessHours?: BusinessHoursSchedule;

  /** Club menu */
  @Prop({ type: Object })
  menu?: Menu;

  /** Whether club is featured */

  @Prop({ default: false })
  featured: boolean;

  /** Club rating (0-5) */

  @Prop({ default: 0, min: 0, max: 5 })
  rating: number;
}

@ObjectType()
export class PaginatedClubs extends createPaginatedType(Club) {}

export const ClubSchema = SchemaFactory.createForClass(Club);
