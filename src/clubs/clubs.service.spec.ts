import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { ClubsService } from './clubs.service';
import { Club, MenuItemAvailability } from './entities/club.entity';
import { CreateClubInput } from './dto/create-club.input';

describe('ClubsService', () => {
  let service: ClubsService;
  let mockClubModel: any;

  beforeEach(async () => {
    mockClubModel = {
      create: jest.fn(),
      paginate: jest.fn(),
      findById: jest.fn(),
      findByIdAndUpdate: jest.fn(),
      findByIdAndDelete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ClubsService,
        {
          provide: getModelToken(Club.name),
          useValue: mockClubModel,
        },
      ],
    }).compile();

    service = module.get<ClubsService>(ClubsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a club with updated menu structure', async () => {
      const createClubInput: CreateClubInput = {
        name: 'Test Club',
        description: 'A test club',
        city: 'test-city-id',
        categories: ['test-category-id'],
        status: 'ACTIVE' as any,
        images: ['image1.jpg'],
        featured: false,
        rating: 4.5,
        menu: {
          currency: 'EUR',
          items: [
            {
              name: 'Heineken',
              price: 5.00,
              available: MenuItemAvailability.AVAILABLE,
            },
            {
              name: 'Marguerite',
              price: 15.00,
              available: MenuItemAvailability.UNAVAILABLE,
            },
          ],
        },
      };

      const expectedResult = { _id: 'test-id', ...createClubInput };
      mockClubModel.create.mockResolvedValue(expectedResult);

      const result = await service.create(createClubInput);

      expect(mockClubModel.create).toHaveBeenCalledWith(createClubInput);
      expect(result).toEqual(expectedResult);
      expect(result.menu).toBeDefined();
      expect(result.menu?.currency).toBe('EUR');
      expect(result.menu?.items).toHaveLength(2);
      expect(result.menu?.items[0].name).toBe('Heineken');
      expect(result.menu?.items[0].available).toBe(MenuItemAvailability.AVAILABLE);
      expect(result.menu?.items[1].name).toBe('Marguerite');
      expect(result.menu?.items[1].available).toBe(MenuItemAvailability.UNAVAILABLE);
      // Verify removed fields are not present
      expect(result.menu?.items[0]).not.toHaveProperty('description');
      expect(result.menu?.items[0]).not.toHaveProperty('category');
      expect(result.menu?.items[0]).not.toHaveProperty('image');
    });

    it('should create a club without menu', async () => {
      const createClubInput: CreateClubInput = {
        name: 'Test Club Without Menu',
        description: 'A test club without menu',
        city: 'test-city-id',
        categories: ['test-category-id'],
        status: 'ACTIVE' as any,
        images: ['image1.jpg'],
        featured: false,
        rating: 4.0,
      };

      const expectedResult = { _id: 'test-id', ...createClubInput };
      mockClubModel.create.mockResolvedValue(expectedResult);

      const result = await service.create(createClubInput);

      expect(mockClubModel.create).toHaveBeenCalledWith(createClubInput);
      expect(result).toEqual(expectedResult);
      expect(result.menu).toBeUndefined();
    });
  });

  describe('update', () => {
    it('should update a club with new menu structure', async () => {
      const updateData = {
        id: 'test-id',
        menu: {
          currency: 'USD',
          items: [
            {
              name: 'Updated Item',
              price: 10.00,
              available: MenuItemAvailability.AVAILABLE,
            },
          ],
        },
      };

      const expectedResult = { _id: 'test-id', ...updateData };
      mockClubModel.findByIdAndUpdate.mockResolvedValue(expectedResult);

      const result = await service.update('test-id', updateData);

      expect(mockClubModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'test-id',
        updateData,
        { new: true }
      );
      expect(result?.menu?.currency).toBe('USD');
      expect(result?.menu?.items[0].name).toBe('Updated Item');
      expect(result?.menu?.items[0].available).toBe(MenuItemAvailability.AVAILABLE);
    });
  });
});
