import { InputType } from '@nestjs/graphql';
import {
  AddressInput,
  DayOfWeek,
  DayTimingInput,
} from 'src/common/common.entity';
import { ZodValidation } from 'src/common/zod-validator/decorators/zod-validation.decorator';
import { ContactInput } from 'src/users/dto/create-user.input';
import { z } from 'zod';
import { ClubStatus } from '../entities/club.entity';

const createClubInputSchema = z.object({
  name: z.string(),
  description: z.string(),
  categories: z.array(z.string()),
  status: z.nativeEnum(ClubStatus),
  city: z.string(),
  logo: z.string().optional(),
  coverImage: z.string().optional(),
  images: z.array(z.string()),
  phone: z
    .object({
      countryCode: z.string(),
      phone: z.string(),
    })
    .optional(),
  address: z
    .object({
      address: z.string(),
      coordinates: z.object({
        latitude: z.number(),
        longitude: z.number(),
      }),
      metroLine: z.string().optional(),
      metroStation: z.string().optional(),
    })
    .optional(),
  businessHours: z
    .object({
      schedule: z.array(
        z.object({
          day: z.nativeEnum(DayOfWeek),
          openingTime: z.string().optional(),
          closingTime: z.string().optional(),
        }),
      ),
    })
    .optional(),
  menu: z
    .object({
      currency: z.string(),
      items: z.array(
        z.object({
          name: z.string(),
          description: z.string().optional(),
          price: z.number().min(0),
          category: z.string().optional(),
          image: z.string().optional(),
          available: z.boolean(),
        }),
      ),
    })
    .optional(),
  featured: z.boolean(),
  rating: z.number().min(0).max(5),
});

@InputType()
export class BusinessHoursInput {
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  schedule: DayTimingInput[];
}

@InputType()
export class MenuItemInput {
  /** Menu item name */
  name: string;

  /** Menu item description */
  description?: string;

  /** Menu item price */
  price: number;

  /** Menu item category */
  category?: string;

  /** Menu item image URL */
  image?: string;

  /** Whether the item is available */
  available: boolean;
}

@InputType()
export class MenuInput {
  /** Menu currency (e.g., USD, EUR, INR) */
  currency: string;

  /** Array of menu items */
  items: MenuItemInput[];
}

@ZodValidation(createClubInputSchema)
@InputType()
export class CreateClubInput {
  /** Club name */
  name: string;

  /** Club description */
  description: string;

  /** Club city IDs */
  city: string;

  /** Club category IDs */
  categories: string[];

  /** Club status */
  status: ClubStatus;

  /** Club logo URL */
  logo?: string;

  /** Club cover image URL */
  coverImage?: string;

  /** Array of club image URLs */
  images: string[];

  /** Club contact information (phone) */
  contact?: ContactInput;

  /** Club address */
  address?: AddressInput;

  /** Opening hours */
  businessHours?: BusinessHoursInput;

  /** Club menu */
  menu?: MenuInput;

  /** Whether club is featured */
  featured: boolean;

  /** Club rating (0-5) */
  rating: number;
}
