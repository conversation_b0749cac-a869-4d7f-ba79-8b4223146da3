import { ObjectType } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Coordinates, MongooseSchema } from 'src/common/common.entity';
import { createPaginatedType } from 'src/common/pagination.input';

@ObjectType()
@Schema()
export class City extends MongooseSchema {
  /** Name of the city */
  @Prop({ required: true, index: true })
  name: string;

  /** Main image of the city */
  @Prop({ required: true })
  image: string;

  /** Cover image for the city */
  @Prop()
  coverImage?: string;

  /** Main heading for the city */
  @Prop({ required: true })
  heading: string;

  /** Sub heading for the city */
  @Prop()
  subHeading?: string;

  /** Geographical coordinates of the city */
  @Prop({ required: true, type: Object })
  coords: Coordinates;
}

@ObjectType()
export class PaginatedCities extends createPaginatedType(City) {}

export const CitySchema = SchemaFactory.createForClass(City);
