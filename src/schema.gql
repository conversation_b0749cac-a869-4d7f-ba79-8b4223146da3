# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Coordinates {
  """Latitude"""
  latitude: Float!

  """Longitude"""
  longitude: Float!
}

type Address {
  """Address"""
  address: String!

  """Coordinates"""
  coordinates: Coordinates!

  """Metro line"""
  metroLine: String

  """Metro station"""
  metroStation: String
}

type Contact {
  """Country code (e.g., +1, +91)"""
  countryCode: String!

  """Phone number"""
  phone: String!
}

type User {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """User fullname"""
  fullname: String!

  """User's email"""
  email: String!

  """User active status"""
  userStatus: UserStatus!

  """User role"""
  role: UserRoles!
  password: String!

  """User contact information"""
  contact: Contact!

  """Whether user has accepted terms and conditions"""
  acceptedTermsAndConditions: Boolean!

  """Whether user wants to receive discounts, royalty offers and updates"""
  subscribeToUpdates: Boolean!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

enum UserStatus {
  ACTIVE
  INACTIVE
}

enum UserRoles {
  ADMIN
  USER
}

type PaginatedUsers {
  """Array of documents"""
  docs: [User!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type AuthOutput {
  """Access token"""
  access_token: String!
}

type PresignedFields {
  key: String!
  bucket: String!
  acl: String!
  algorithm: String!
  credential: String!
  date: String!
  Policy: String!
  signature: String!
}

type SignedUploadUrl {
  url: String!
  fields: PresignedFields!
}

type ClubCategory {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """Club Category name"""
  name: String!

  """Club Category description"""
  description: String!
}

type City {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """Name of the city"""
  name: String!

  """Main image of the city"""
  image: String!

  """Cover image for the city"""
  coverImage: String

  """Main heading for the city"""
  heading: String!

  """Sub heading for the city"""
  subHeading: String

  """Geographical coordinates of the city"""
  coords: Coordinates!
}

type PaginatedCities {
  """Array of documents"""
  docs: [City!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type DayTiming {
  timings: [Float!]!

  """Day of the week"""
  day: DayOfWeek!
}

enum DayOfWeek {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}

type BusinessHoursSchedule {
  """
  Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format
  """
  schedule: [DayTiming!]!
}

type MenuItem {
  """Menu item name"""
  name: String!

  """Menu item price"""
  price: Float!

  """Whether the item is available"""
  available: MenuItemAvailability!
}

enum MenuItemAvailability {
  AVAILABLE
  UNAVAILABLE
}

type Menu {
  """Menu currency (e.g., USD, EUR, INR)"""
  currency: String!

  """Array of menu items"""
  items: [MenuItem!]!
}

type Club {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """Club category"""
  categories: [ClubCategory!]!
  city: [City!]!

  """Club name"""
  name: String!

  """Club description"""
  description: String!

  """Club status"""
  status: ClubStatus!

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]!

  """Club phone number"""
  phone: Contact

  """Club address"""
  address: Address

  """Opening hours"""
  businessHours: BusinessHoursSchedule

  """Club menu"""
  menu: Menu

  """Whether club is featured"""
  featured: Boolean!

  """Club rating (0-5)"""
  rating: Float!
}

enum ClubStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}

type PaginatedClubs {
  """Array of documents"""
  docs: [Club!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type Query {
  clubs(clubsInput: ClubsInput): PaginatedClubs!
  club(id: String!): Club!

  """Get all cities with pagination and filtering"""
  cities(citiesInput: CitiesInput, paginationInput: PaginationInput): PaginatedCities!

  """Get a single city by ID"""
  city(id: ID!): City!
  users(usersInput: UsersInput, paginationInput: PaginationInput): PaginatedUsers!
  user(id: String!): User!

  """Logged in  user"""
  me: User!
}

input ClubsInput {
  """Page number (1-based)"""
  page: Float! = 1

  """Number of items per page"""
  limit: Float! = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!]! = [{field: "createdAt", order: DESC}]
}

input SortConfigInput {
  """Field to sort by"""
  field: String!

  """
  Sort order: "asc" or "desc"
  """
  order: SortOrder! = DESC
}

enum SortOrder {
  ASC
  DESC
}

input CitiesInput {
  """Filter by city name"""
  name: String
}

input PaginationInput {
  """Page number (1-based)"""
  page: Float! = 1

  """Number of items per page"""
  limit: Float! = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!]! = [{field: "createdAt", order: DESC}]
}

input UsersInput {
  """Page number (1-based)"""
  page: Float! = 1

  """Number of items per page"""
  limit: Float! = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!]! = [{field: "createdAt", order: DESC}]
}

type Mutation {
  createClub(createClubInput: CreateClubInput!): Boolean!
  updateClub(updateClubInput: UpdateClubInput!): Club!
  removeClub(id: String!): Club!

  """Create a new city"""
  createCity(createCityInput: CreateCityInput!): City!

  """Update an existing city"""
  updateCity(updateCityInput: UpdateCityInput!): City!

  """Delete a city"""
  deleteCity(id: ID!): City!
  createSignedUploadUrl(input: SignedUploadUrlInput!): SignedUploadUrl!
  signIn(input: SignInInput!): AuthOutput!
  signUp(input: CreateUserInput!): AuthOutput!
}

input CreateClubInput {
  """Club name"""
  name: String!

  """Club description"""
  description: String!

  """Club city IDs"""
  city: String!

  """Club category IDs"""
  categories: [String!]!

  """Club status"""
  status: ClubStatus!

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]!

  """Club contact information (phone)"""
  contact: ContactInput

  """Club address"""
  address: AddressInput

  """Opening hours"""
  businessHours: BusinessHoursInput

  """Club menu"""
  menu: MenuInput

  """Whether club is featured"""
  featured: Boolean!

  """Club rating (0-5)"""
  rating: Float!
}

input ContactInput {
  """Country code (e.g., +1, +91)"""
  countryCode: String!

  """Phone number"""
  phone: String!
}

input AddressInput {
  """Address"""
  address: String!

  """Coordinates"""
  coordinates: CoordinatesInput!

  """Metro line"""
  metroLine: String

  """Metro station"""
  metroStation: String
}

"""Inputs"""
input CoordinatesInput {
  """Latitude"""
  latitude: Float!

  """Longitude"""
  longitude: Float!
}

input BusinessHoursInput {
  """
  Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format
  """
  schedule: [DayTimingInput!]!
}

input DayTimingInput {
  """Day of the week"""
  day: DayOfWeek!

  """
  Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30). Supports formats that can be converted by time.utils: 900, 1730, etc.
  """
  timings: [Float!]!
}

input MenuInput {
  """Menu currency (e.g., USD, EUR, INR)"""
  currency: String!

  """Array of menu items"""
  items: [MenuItemInput!]!
}

input MenuItemInput {
  """Menu item name"""
  name: String!

  """Menu item price"""
  price: Float!

  """Whether the item is available"""
  available: MenuItemAvailability!
}

input UpdateClubInput {
  """Club name"""
  name: String

  """Club description"""
  description: String

  """Club city IDs"""
  city: String

  """Club category IDs"""
  categories: [String!]

  """Club status"""
  status: ClubStatus

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]

  """Club contact information (phone)"""
  contact: ContactInput

  """Club address"""
  address: AddressInput

  """Opening hours"""
  businessHours: BusinessHoursInput

  """Club menu"""
  menu: MenuInput

  """Whether club is featured"""
  featured: Boolean

  """Club rating (0-5)"""
  rating: Float
  id: String!
}

input CreateCityInput {
  """Name of the city"""
  name: String!

  """Main image of the city"""
  image: String!

  """Cover image for the city"""
  coverImage: String

  """Main heading for the city"""
  heading: String!

  """Sub heading for the city"""
  subHeading: String

  """Geographical coordinates of the city"""
  coords: CoordinatesInput!
}

input UpdateCityInput {
  """Name of the city"""
  name: String

  """Main image of the city"""
  image: String

  """Cover image for the city"""
  coverImage: String

  """Main heading for the city"""
  heading: String

  """Sub heading for the city"""
  subHeading: String

  """Geographical coordinates of the city"""
  coords: CoordinatesInput

  """City ID"""
  id: ID!
}

input SignedUploadUrlInput {
  key: String!
  contentType: String!
  expiresIn: Float
}

input SignInInput {
  """User phone number"""
  email: String!

  """User password"""
  password: String!
}

input CreateUserInput {
  """User fullname"""
  fullname: String!

  """User phone number"""
  email: String!

  """User role"""
  role: UserRoles!

  """User password"""
  password: String!

  """User active status"""
  userStatus: UserStatus!

  """User contact information"""
  contact: ContactInput!

  """Whether user has accepted terms and conditions"""
  acceptedTermsAndConditions: Boolean!

  """Whether user wants to receive discounts, royalty offers and updates"""
  subscribeToUpdates: Boolean!
}