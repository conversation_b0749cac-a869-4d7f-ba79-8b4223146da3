import { Global, Module } from '@nestjs/common';
import { ClubsModule } from 'src/clubs/clubs.module';
import { DataloaderService } from './dataloader.service';
import { CityModule } from 'src/city/city.module';
import { UsersModule } from 'src/users/users.module';

@Global()
@Module({
  imports: [ClubsModule, CityModule, UsersModule],
  providers: [DataloaderService],
  exports: [DataloaderService],
})
export class DataloaderModule {}
